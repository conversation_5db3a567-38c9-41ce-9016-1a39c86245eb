"use client";

import ReactModal from "react-modal";
import React, { useState, useEffect, useMemo } from "react";
import {
  CloseIcon,
  CoinTip,
  FlashIcon,
  WalletIcon,
  ReloadIcon,
  SearchIcon,
  SlippageIcon,
  GasIcon,
} from "@/assets/icons";
import { NumericFormat } from "react-number-format";
import { AppButton, AppLogoNetwork, AppNumber, AppToggle } from "@/components";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { formatNumber, formatShortAddress } from "@/utils/format";
import { TWallet } from "@/types";
import { toastError, toastSuccess } from "@/libs/toast";
import Storage from "@/libs/storage";
import { ICheckboxIcon, ICheckedIcon } from "@/public/images";
import { useMediaQuery } from "react-responsive";
import BigNumber from "bignumber.js";
import { calculateMaxGasFeeForNetwork } from "@/utils/helper";
import Image from "next/image";

import { useNetwork } from "@/context";
import { useSettingsOrder } from "@/hooks";
import {
  validateOrderSettings,
  getGasPriceUnit,
  getGasPriceValidationMessage,
  getTipAmountValidationMessage,
} from "@/utils/orderSettingsValidator";
import { getNetworkSymbol } from "@/app/providers/networkChains";

const Wallets = ({
  walletsSelected,
  setWalletsSelected,
}: {
  walletsSelected: string[];
  setWalletsSelected: (data: string[]) => void;
}) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { currentNetwork } = useNetwork();
  const [walletsShow, setWalletsShow] = useState<TWallet[]>([]);
  const [search, setSearch] = useState<string>("");

  const walletAddresses = useMemo(() => {
    return wallets.map((item) => item.address);
  }, [wallets]);

  useEffect(() => {
    let dataWallet = wallets;
    if (search) {
      dataWallet = dataWallet.filter((item) =>
        item.address?.toLowerCase().includes(search?.toLowerCase())
      );
    }

    setWalletsShow(dataWallet);
  }, [wallets, search]);

  const onSelectAllWallet = () => {
    if (walletAddresses.every((item) => walletsSelected.includes(item))) {
      setWalletsSelected([]);
      return;
    }

    setWalletsSelected(walletAddresses);
  };

  const onSelectWallet = (wallet: TWallet) => {
    let data = walletsSelected;
    if (walletsSelected.includes(wallet.address)) {
      data = data.filter((address) => address !== wallet.address);
    } else {
      data = data.concat([wallet.address]);
    }

    setWalletsSelected(data);
  };

  return (
    <div>
      <div className="item-center mb-[4px] flex justify-between">
        <div className="body-sm-regular-12 text-white-500 flex items-center gap-[8px]">
          <div onClick={onSelectAllWallet} className="cursor-pointer">
            {!!walletAddresses.length &&
            walletAddresses.every((item) => walletsSelected.includes(item)) ? (
              <Image
                src={ICheckedIcon}
                alt="checked-icon"
                width={12}
                height={12}
                unoptimized
              />
            ) : (
              <Image
                src={ICheckboxIcon}
                alt="checkbox-icon"
                width={12}
                height={12}
                unoptimized
              />
            )}
          </div>
          All wallet ({wallets.length})
        </div>

        <div className="border-white-100 flex items-center gap-1 border-b pb-[2px]">
          <SearchIcon className="text-white-500" />
          <input
            className="body-xs-regular-10 placeholder:text-white-300 w-[134px] truncate bg-transparent outline-none"
            placeholder="Search"
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
            }}
          />
        </div>
      </div>

      <div className="customer-scroll h-[168px] overflow-auto">
        {!!walletsShow.length ? (
          walletsShow.map((item, index) => {
            return (
              <div
                className="border-white-50 grid grid-cols-3 border-b border-dashed px-[6px] py-[8px]"
                key={index}
              >
                <div className="body-sm-medium-12 flex items-center gap-2">
                  <div
                    onClick={() => onSelectWallet(item)}
                    className="cursor-pointer"
                  >
                    {walletsSelected.includes(item.address) ? (
                      <Image
                        unoptimized
                        src={ICheckedIcon}
                        alt="checked-icon"
                        width={12}
                        height={12}
                      />
                    ) : (
                      <Image
                        unoptimized
                        src={ICheckboxIcon}
                        alt="checkbox-icon"
                        width={12}
                        height={12}
                      />
                    )}
                  </div>
                  <span className="truncate">{item.aliasName}</span>
                </div>
                <div className="body-xs-regular-10 text-white-700 text-center">
                  {formatShortAddress(item.address, 5, 3)}
                </div>
                <div className="body-sm-regular-12 flex justify-end gap-[4px]">
                  <AppNumber value={item.balance} className="text-white-1000" />
                  <div className="text-white-500">
                    {getNetworkSymbol(currentNetwork)}
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="body-sm-medium-12 py-2 text-center">No wallets</div>
        )}
      </div>
    </div>
  );
};

export const ModalQuickBuySettings = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 420px)" });

  const customStyles = {
    content: {
      ...(isMobile
        ? {
            top: "auto",
            left: "0",
            right: "0",
            bottom: "0",
            borderRadius: "16px 16px 0 0",
          }
        : {
            top: "50%",
            left: "50%",
            right: "auto",
            bottom: "auto",
            marginRight: "-50%",
            transform: "translate(-50%, -50%)",
            borderRadius: "16px",
          }),
      padding: 0,
      overflow: "inherit",
      border: "1px solid rgba(255, 255, 255, 0.05)",
      boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
      background: "#141518",
    },

    overlay: {
      background: "rgba(8, 9, 12, 0.70)",
      backdropFilter: "blur(7.5px)",
      zIndex: 999,
    },
  };

  const [slippage, setSlippage] = useState<any>("40");
  const [tipAmount, setTipAmount] = useState<any>("0.02");
  const [gasPrice, setGasPrice] = useState<any>("750");
  const [isEnableTip, setIsEnableTip] = useState<boolean>(true);
  const [walletsSelected, setWalletsSelected] = useState<string[]>([]);

  const { currentNetwork } = useNetwork();
  const wallets = useSelector((state: RootState) => state.user.wallets);

  const maxGasFeeBuySell = useMemo(() => {
    return calculateMaxGasFeeForNetwork(gasPrice, currentNetwork);
  }, [gasPrice, currentNetwork]);

  const { updateSettingsQuickOrder, settingsQuickOrder } = useSettingsOrder();

  useEffect(() => {
    setTipAmount(settingsQuickOrder?.tipAmount);
    setSlippage(settingsQuickOrder?.slippage);
    setGasPrice(settingsQuickOrder?.gasPrice);
  }, [settingsQuickOrder]);

  useEffect(() => {
    if (new BigNumber(settingsQuickOrder?.tipAmount).gt(0)) {
      setIsEnableTip(true);
      return;
    }
    setIsEnableTip(false);
  }, [settingsQuickOrder]);

  const walletAddresses = Storage.getWalletAddresses() || [wallets[0]?.address];

  useEffect(() => {
    setWalletsSelected(walletAddresses);
  }, []);

  const onApply = async () => {
    if (!walletsSelected.length) {
      toastError("Error", "Please select wallet!");
      return;
    }

    const validation = validateOrderSettings(currentNetwork, {
      gasPrice,
      tipAmount,
      slippage,
      isEnableTip,
    });

    if (!validation.isValid) {
      validation.errors.forEach((error) => {
        toastError("Error", error);
      });
      return;
    }

    try {
      await updateSettingsQuickOrder({
        ...settingsQuickOrder,
        slippage: +slippage,
        tipAmount: isEnableTip ? tipAmount : 0,
        gasPrice,
      });
      Storage.setWalletAddresses(walletsSelected || "");
      onClose();
      toastSuccess("Success", "Update successfully!");
    } catch (e) {
      console.error(e);
    }
  };

  const onReset = () => {
    setWalletsSelected(walletAddresses);
    setTipAmount(settingsQuickOrder?.tipAmount);
    setSlippage(settingsQuickOrder?.slippage);
    setGasPrice(settingsQuickOrder?.gasPrice);
  };

  return (
    <ReactModal
      isOpen={isOpen}
      onRequestClose={onClose}
      style={customStyles}
      ariaHideApp={false}
      bodyOpenClassName="overflow-hidden"
    >
      <div className="w-[calc(100vw)] max-w-[420px] p-[16px] pt-[12px]">
        <div className="mb-[12px] flex justify-between">
          <div className="heading-sm-medium-16 flex items-center gap-[8px]">
            <FlashIcon className="text-brand-500" /> Quick buy Setting
          </div>
          <div
            className="text-white-500 hover:text-white-1000 cursor-pointer"
            onClick={onClose}
          >
            <CloseIcon />
          </div>
        </div>

        <div className="mb-[16px]">
          <div className="action-xs-medium-12 text-white-700 mb-[8px] flex items-center gap-[4px]">
            <SlippageIcon />
            Slippage limit
          </div>
          <div className="border-white-50 flex items-center gap-[8px] rounded-[8px] border p-[8px]">
            <div className="text-white-500 body-sm-regular-12">%</div>
            <NumericFormat
              placeholder="Enter slippage limit"
              value={slippage}
              onValueChange={({ value }) => setSlippage(value)}
              thousandSeparator=","
              valueIsNumericString
              decimalScale={2}
              className="body-sm-regular-12 placeholder:text-white-300 text-neutral-0 w-full bg-transparent outline-none"
            />
          </div>
        </div>

        <div className="mb-[16px]">
          <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
            <GasIcon /> Gas Price
          </div>

          <div className="text-white-700 body-xs-regular-10 mb-2">
            {getGasPriceValidationMessage(currentNetwork)}
          </div>

          <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
            <div className="body-sm-regular-12 text-neutral-alpha-500">
              {getGasPriceUnit(currentNetwork)}
            </div>
            <NumericFormat
              value={gasPrice ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-md-semibold-14 w-full bg-transparent outline-none"
              decimalScale={2}
              onValueChange={({ floatValue }) => {
                return setGasPrice(floatValue);
              }}
            />
          </div>

          <div className="item-center flex justify-between">
            <div className="body-xs-regular-10 text-white-700 mt-1">
              Est Gas Fee:
            </div>
            <div className="text-white-1000 body-xs-medium-10 mt-1">
              ~{formatNumber(maxGasFeeBuySell)}{" "}
              {getNetworkSymbol(currentNetwork)}
            </div>
          </div>
        </div>

        <div className="mb-[16px]">
          <div className="flex items-center justify-between">
            <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
              <CoinTip /> Tip amount
            </div>
            <AppToggle
              value={isEnableTip}
              onChange={() => setIsEnableTip(!isEnableTip)}
            />
          </div>

          <div className="body-xs-regular-10 text-white-700">
            {getTipAmountValidationMessage(currentNetwork)}
          </div>

          {isEnableTip && (
            <div className="border-neutral-alpha-50 mt-2 flex items-center gap-2 rounded-[6px] border p-2">
              <div className="body-sm-regular-12 text-neutral-alpha-500">
                <AppLogoNetwork
                  network={currentNetwork}
                  isBase
                  className="h-[12px] w-[12px]"
                />
              </div>
              <NumericFormat
                value={tipAmount ?? ""}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                className="body-md-semibold-14 w-full bg-transparent outline-none"
                decimalScale={6}
                onValueChange={({ floatValue }) => {
                  return setTipAmount(floatValue);
                }}
              />
            </div>
          )}
        </div>

        <div className="mb-[8px] flex justify-between">
          <div className="action-xs-medium-12 text-white-700 flex items-center gap-[4px]">
            <WalletIcon />
            Wallet using
          </div>
          <div className="body-xs-regular-10 text-white-500">
            {walletsSelected.length} selected
          </div>
        </div>

        <div className="border-white-100 rounded-[6px] border p-[4px] pb-[8px]">
          <Wallets
            walletsSelected={walletsSelected}
            setWalletsSelected={setWalletsSelected}
          />
        </div>

        <div className="mt-[20px] flex items-center justify-between">
          <div
            className="body-md-regular-14 flex cursor-pointer items-center gap-[8px]"
            onClick={onReset}
          >
            <ReloadIcon className="h-[16px] w-[16px]" />
            Reset
          </div>
          <AppButton size="large" onClick={onApply}>
            Apply
          </AppButton>
        </div>
      </div>
    </ReactModal>
  );
};
