"use client";

import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { AppButton } from "@/components";
import { toastError, toastSuccess } from "@/libs/toast";
import { BaseModal } from "@/modals/BaseModal";
import rf from "@/services/RequestFactory";
import { getWalletsUser } from "@/store/user.store";
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { AppDispatch } from "@/store";
import { useNetwork } from "@/context";
import { NETWORKS } from "@/utils/contants";
import { isHex, isAddress } from "viem";
import { privateKeyToAccount } from "viem/accounts";

export const ModalImportWallet = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const [privateKey, setPrivateKey] = useState<string>("");
  const dispatch = useDispatch<AppDispatch>();
  const { currentNetwork } = useNetwork();

  const onImportWallet = async () => {
    try {
      const validationResult = validatePrivateKeyByNetwork(
        privateKey,
        currentNetwork
      );
      if (!validationResult.isValid) {
        throw new Error(validationResult.errorMessage);
      }
      await rf.getRequest("WalletRequest").getImportWallet(currentNetwork, {
        privateKey: validationResult.standardPrivateKey,
      });
      dispatch(getWalletsUser({ network: currentNetwork }));
      toastSuccess("Success", "Import successfully!");
      if (typeof onSuccess === "function") {
        onSuccess();
      }
      onClose();
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const validatePrivateKeyByNetwork = (privateKey: string, network: string) => {
    try {
      if (network === NETWORKS.SUI) {
        if (privateKey.startsWith("suiprivkey")) {
          const standardPrivateKey =
            Ed25519Keypair.fromSecretKey(privateKey).getSecretKey();
          return {
            isValid: true,
            standardPrivateKey,
            errorMessage: "",
          };
        }
        return {
          isValid: false,
          standardPrivateKey: "",
          errorMessage:
            "Private key format is incorrect for Sui network. Please make sure it starts with 'suiprivkey' and not a passphrase.",
        };
      }

      if (
        [NETWORKS.HYPEREVM, NETWORKS.SOMNIA, NETWORKS.PLASMA].includes(
          network as any
        )
      ) {
        try {
          const formattedKey = privateKey.startsWith("0x")
            ? privateKey
            : `0x${privateKey}`;

          if (!isHex(formattedKey) || formattedKey.length !== 66) {
            return {
              isValid: false,
              standardPrivateKey: "",
              errorMessage:
                "Private key format is incorrect for EVM network. Please provide a valid 64-character hexadecimal private key (with or without 0x prefix).",
            };
          }

          const account = privateKeyToAccount(formattedKey as `0x${string}`);

          if (!isAddress(account.address)) {
            return {
              isValid: false,
              standardPrivateKey: "",
              errorMessage:
                "Invalid private key - unable to generate valid address.",
            };
          }

          return {
            isValid: true,
            standardPrivateKey: formattedKey,
            errorMessage: "",
          };
        } catch (error) {
          return {
            isValid: false,
            standardPrivateKey: "",
            errorMessage: "Invalid private key format for EVM network.",
          };
        }
      }

      return {
        isValid: false,
        standardPrivateKey: "",
        errorMessage: "Unsupported network type.",
      };
    } catch (e) {
      return {
        isValid: false,
        standardPrivateKey: "",
        errorMessage: "Invalid private key format.",
      };
    }
  };

  const getPlaceholderText = (network: string) => {
    if (network === NETWORKS.SUI) {
      return "Enter your Sui private key (starts with 'suiprivkey')";
    }
    if (
      network === NETWORKS.HYPEREVM ||
      network === NETWORKS.SOMNIA ||
      network === NETWORKS.PLASMA
    ) {
      return "Enter your EVM private key (64-character hex string)";
    }
    return "Enter your private key";
  };

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={"Import Wallet"}
      onClose={onClose}
      description=""
      descClassName="max-w-[300px]"
    >
      <div className="flex flex-col gap-1">
        <div>
          <div className="body-sm-medium-12 text-white-700 mb-[8px]">
            Input your private keys
          </div>
          <textarea
            placeholder={getPlaceholderText(currentNetwork)}
            className="bg-black-900 placeholder:text-white-300 body-sm-regular-12 border-white-100 h-[275px] w-full rounded-[6px] border p-[8px] outline-none"
            value={privateKey}
            onChange={(e) => setPrivateKey(e.target.value.trim())}
          />
        </div>
      </div>
      <div className="mt-[28px] flex justify-center">
        <AppButton
          disabled={!privateKey}
          className="min-w-[167px]"
          onClick={onImportWallet}
          size="large"
        >
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};
