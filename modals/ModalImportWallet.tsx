"use client";

import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { AppButton } from "@/components";
import { toastError, toastSuccess } from "@/libs/toast";
import { BaseModal } from "@/modals/BaseModal";
import rf from "@/services/RequestFactory";
import { getWalletsUser } from "@/store/user.store";
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { AppDispatch } from "@/store";
import { useNetwork } from "@/context";
import { NETWORKS } from "@/utils/contants";
import { isHex } from "viem";

export const ModalImportWallet = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const [privateKey, setPrivateKey] = useState<string>("");
  const dispatch = useDispatch<AppDispatch>();
  const { currentNetwork } = useNetwork();

  const onImportWallet = async () => {
    try {
      const validationResult = validatePrivateKeyByNetwork(
        privateKey,
        currentNetwork
      );
      if (!validationResult.isValid) {
        throw new Error(validationResult.errorMessage);
      }
      await rf.getRequest("WalletRequest").getImportWallet(currentNetwork, {
        privateKey: validationResult.standardPrivateKey,
      });
      dispatch(getWalletsUser({ network: currentNetwork }));
      toastSuccess("Success", "Import successfully!");
      if (typeof onSuccess === "function") {
        onSuccess();
      }
      onClose();
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const validatePrivateKeyByNetwork = (privateKey: string, network: string) => {
    try {
      // Sui network validation
      if (network === NETWORKS.SUI) {
        if (privateKey.startsWith("suiprivkey")) {
          const standardPrivateKey =
            Ed25519Keypair.fromSecretKey(privateKey).getSecretKey();
          return {
            isValid: true,
            standardPrivateKey,
            errorMessage: "",
          };
        }
        return {
          isValid: false,
          standardPrivateKey: "",
          errorMessage:
            "Private key format is incorrect for Sui network. Please make sure it starts with 'suiprivkey' and not a passphrase.",
        };
      }

      // EVM networks validation (Hype, Somnia, Plasma)
      if (
        network === NETWORKS.HYPEREVM ||
        network === NETWORKS.SOMNIA ||
        network === NETWORKS.PLASMA
      ) {
        // Remove 0x prefix if present
        const cleanKey = privateKey.startsWith("0x")
          ? privateKey.slice(2)
          : privateKey;

        // Check if it's a valid 64-character hex string (32 bytes)
        if (cleanKey.length === 64 && isHex(`0x${cleanKey}`)) {
          return {
            isValid: true,
            standardPrivateKey: `0x${cleanKey}`,
            errorMessage: "",
          };
        }

        return {
          isValid: false,
          standardPrivateKey: "",
          errorMessage:
            "Private key format is incorrect for EVM network. Please provide a valid 64-character hexadecimal private key (with or without 0x prefix).",
        };
      }

      return {
        isValid: false,
        standardPrivateKey: "",
        errorMessage: "Unsupported network type.",
      };
    } catch (e) {
      return {
        isValid: false,
        standardPrivateKey: "",
        errorMessage: "Invalid private key format.",
      };
    }
  };

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={"Import Wallet"}
      onClose={onClose}
      description=""
      descClassName="max-w-[300px]"
    >
      <div className="flex flex-col gap-1">
        <div>
          <div className="body-sm-medium-12 text-white-700 mb-[8px]">
            Input your private keys
          </div>
          <textarea
            placeholder="Enter your private key"
            className="bg-black-900 placeholder:text-white-300 body-sm-regular-12 border-white-100 h-[275px] w-full rounded-[6px] border p-[8px] outline-none"
            value={privateKey}
            onChange={(e) => setPrivateKey(e.target.value.trim())}
          />
        </div>
      </div>
      <div className="mt-[28px] flex justify-center">
        <AppButton
          disabled={!privateKey}
          className="min-w-[167px]"
          onClick={onImportWallet}
          size="large"
        >
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};
