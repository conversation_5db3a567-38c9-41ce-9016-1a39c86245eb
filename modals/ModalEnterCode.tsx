"use client";

import React, { useEffect, useState } from "react";
import { AppButton } from "@/components";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import { setIsShowModalEnterCode } from "@/store/metadata.store";
import { setUserAuth } from "@/store/user.store";
import { BaseModal } from "@/modals/BaseModal";
import { CloseCircleIcon } from "@/assets/icons";
import rf from "@/services/RequestFactory";
import Storage from "@/libs/storage";
import { useSearchParams } from "next/navigation";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { LOGIN_METHODS, NETWORKS } from "@/utils/contants";
import { closeSocketInstance, createSocketInstance } from "@/libs/socket";
import { useNetwork } from "@/context";

export const ModalEnterCode = () => {
  const [code, setCode] = useState<string>("");
  const [error, setError] = useState<string>("");
  const isOpen = useSelector(
    (state: RootState) => state.metadata.isShowModalEnterCode
  );

  const searchParams = useSearchParams();
  const codeLogin = searchParams.get("code");
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();
  const { currentNetwork } = useNetwork();

  const dispatch = useDispatch<AppDispatch>();

  const onClose = () => {
    dispatch(setIsShowModalEnterCode({ isShow: false }));
  };

  useEffect(() => {
    if (codeLogin) {
      setCode(codeLogin);
    }
  }, [codeLogin]);

  const onLogin = async () => {
    if (!code) {
      return;
    }
    try {
      const res = await rf.getRequest("AuthRequest").login(code);
      dispatch(setUserAuth({ accessToken: res?.jwtToken }));

      Storage.setLoginMethod(LOGIN_METHODS.TELEGRAM);

      if (!!currentAccount?.address) {
        disconnect();
      }
      const redirectAfterLogin = Storage.getRedirectAfterLogin();
      if (redirectAfterLogin) {
        const location = `${window.location.pathname}${window.location.search}`;
        if (location !== redirectAfterLogin) {
          Storage.clearRedirectAfterLogin();
          window.location.href = redirectAfterLogin;
        }
      }
      closeSocketInstance(currentNetwork);
      createSocketInstance(currentNetwork, res?.jwtToken);
      onClose();
    } catch (e: any) {
      setError(e?.message);
      console.error(e);
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Enter Login Code"
      description="Enter the Login Code to verify login access"
      descClassName="max-w-[362px] mt-2"
      closable={false}
      className="w-[calc(100vw-32px)] max-w-[394px]"
    >
      <div className="flex flex-col items-center gap-[32px]">
        <div className="w-full">
          <div className="bg-black-900 border-white-100 flex w-full items-center gap-2 rounded-[6px] border p-2">
            <input
              className="bg-black-900 placeholder:text-white-300 body-sm-regular-12 flex-1  outline-none"
              value={code}
              onChange={(e) => {
                setCode(e.target.value.trim());
                setError("");
              }}
              placeholder="Enter code here"
            />

            {code && (
              <div
                className="cursor-pointer"
                onClick={() => {
                  setCode("");
                  setError("");
                }}
              >
                <CloseCircleIcon className="text-white-600 hover:text-white-0" />
              </div>
            )}
          </div>

          <div className="body-xs-regular-10 text-red-600">{error}</div>
        </div>

        <AppButton
          className="w-full"
          variant="buy"
          size="large"
          onClick={onLogin}
        >
          Login
        </AppButton>
      </div>
    </BaseModal>
  );
};
