import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class PrivyRequest extends BaseRequest {
  getUrlPrefix() {
    return "https://api-privy.raidenx.io/api/v1";
  }

  async login(idToken: string) {
    const url = `/privy/login`;
    return this.post(url, { idToken });
  }

  async createWallet(numberWallets: number) {
    const url = `/privy/create-wallet`;
    return this.post(url, { numberWallets });
  }
}
