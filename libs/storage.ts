import { CANDLE_TYPE, CANDLE_UNIT } from "@/components/TradingView/consts";
import { OrderLimitTargetType } from "@/enums";
import { NETWORKS, UNIT_TYPE } from "@/utils/contants";

const PREFERENCES = `raidenx`;

export type TOrderSettings = {
  defaultBuyAmount: number[];
  defaultSellPercent: number[];
  autoSell: {
    isSettingAutoSell: boolean;
    receiveToken: null | string;
    triggers: TTriggerAutoSell[];
  };
};

type TTriggerAutoSell = {
  priceChangePercent: string | number;
  sellPercent: string | number;
};

type TTriggerAutoSellFunzone = {
  curvePercent: string | number;
  sellPercent: string | number;
};

export type TSnipeSettings = {
  defaultSnipeAmount: number[];
  snipeLiquidity: {
    autoSell: {
      isSettingAutoSell: boolean;
      receiveToken: null | string;
      triggers: TTriggerAutoSell[];
    };
  };
  snipeMigrationDex: {
    autoSell: {
      isSettingAutoSell: boolean;
      receiveToken: null | string;
      triggers: TTriggerAutoSell[];
    };
  };
  snipeDex: {
    autoSell: {
      isSettingAutoSell: boolean;
      receiveToken: null | string;
      triggers: TTriggerAutoSell[];
    };
  };
  snipeFunzone: {
    autoSell: {
      isSettingAutoSell: boolean;
      triggers: TTriggerAutoSellFunzone[];
    };
  };
};

export type TMultiChart = {
  name: string;
  id: string;
  pairs: string[];
};

export type THistorySearch = {
  name: string;
  slug: string;
  dex: string;
  network: string;
  image: string;
};

export type TPairSearch = {
  dexes?: string;
  mintAuthDisabled?: boolean;
  freezeAuthDisabled?: boolean;
  lpBurned?: boolean;
  top10Holders?: boolean;
  devBalance?: boolean;
  liquidity?: string;
  volume?: string;
  marketCap?: string;
  txns?: string;
};

export type TMemeSearch = {
  dexes?: string;
  liquidity?: string;
  volume?: string;
  marketCap?: string;
  txns?: string;
};

type StorageInterface = {
  referralCode?: string;
  accessToken?: string;
  loginMethod?: string;
  network?: string;
  orderSettings: TOrderSettings;
  snipeSettings: TSnipeSettings;
  favourites: { [key: string]: string[] };
  searchHistories?: THistorySearch[];
  walletAddresses?: string[];
  pairSearch: {
    pair_filter?: TPairSearch;
    trendingRankingBy?: string;
  };
  memeSearch: {
    funzone_filter?: TMemeSearch;
  };
  userSettings: {
    chartSetting: {
      type: CANDLE_TYPE;
      unit: CANDLE_UNIT;
    };
    showMyTrades: boolean;
    showAvgPriceLine: boolean;
    showMyOrders: boolean;
    totalUnit: string;
    priceUnit: string;
    orderLimitTargetType: OrderLimitTargetType;
    tokenQuoteSelected: string;
  };
  isHideLeftPanel: boolean;
  isHideOtherTokenForOpenOrder: boolean;
  isHideOtherTokenForPosition: boolean;
  multiChart: {
    columns: number;
    height: number;
    data: TMultiChart[];
  };
  dontShowIssuesDetectedAgain: any;
  redirectAfterLogin: any;
  isHideInstallApp: boolean;
};

const defaultPreferences: StorageInterface = {
  snipeSettings: {
    defaultSnipeAmount: [1, 2, 5, 10],
    snipeLiquidity: {
      autoSell: {
        isSettingAutoSell: false,
        receiveToken: null,
        triggers: [
          {
            priceChangePercent: "10",
            sellPercent: "50",
          },
          {
            priceChangePercent: "-10",
            sellPercent: "50",
          },
        ],
      },
    },
    snipeMigrationDex: {
      autoSell: {
        isSettingAutoSell: false,
        receiveToken: null,
        triggers: [
          {
            priceChangePercent: "10",
            sellPercent: "50",
          },
          {
            priceChangePercent: "-10",
            sellPercent: "50",
          },
        ],
      },
    },
    snipeDex: {
      autoSell: {
        isSettingAutoSell: false,
        receiveToken: null,
        triggers: [
          {
            priceChangePercent: "10",
            sellPercent: "50",
          },
          {
            priceChangePercent: "-10",
            sellPercent: "50",
          },
        ],
      },
    },
    snipeFunzone: {
      autoSell: {
        isSettingAutoSell: false,
        triggers: [
          {
            curvePercent: "90",
            sellPercent: "100",
          },
        ],
      },
    },
  },
  orderSettings: {
    defaultBuyAmount: [1, 2, 5, 10],
    defaultSellPercent: [25, 50, 75, 100],
    autoSell: {
      isSettingAutoSell: false,
      receiveToken: null,
      triggers: [
        {
          priceChangePercent: "10",
          sellPercent: "50",
        },
        {
          priceChangePercent: "-10",
          sellPercent: "50",
        },
      ],
    },
  },
  favourites: {},
  searchHistories: [],
  pairSearch: {
    pair_filter: {} as TPairSearch,
    trendingRankingBy: "24h",
  },
  memeSearch: {
    funzone_filter: {} as TMemeSearch,
  },
  userSettings: {
    chartSetting: {
      type: CANDLE_TYPE.PRICE,
      unit: CANDLE_UNIT.USD,
    },
    showMyTrades: true,
    showAvgPriceLine: true,
    showMyOrders: true,
    totalUnit: UNIT_TYPE.USD,
    priceUnit: UNIT_TYPE.USD,
    orderLimitTargetType: OrderLimitTargetType.MC,
    tokenQuoteSelected: "",
  },
  isHideLeftPanel: false,
  isHideOtherTokenForOpenOrder: false,
  isHideOtherTokenForPosition: false,
  multiChart: {
    columns: 2,
    height: 475,
    data: [
      {
        name: "Tab 1",
        id: "tab-1",
        pairs: [],
      },
    ],
  },
  dontShowIssuesDetectedAgain: {},
  redirectAfterLogin: "",
  isHideInstallApp: false,
};

function getStorage(): StorageInterface | any {
  if (typeof window === "undefined") return {};
  const preferencesString = localStorage.getItem(PREFERENCES);
  const preferences = JSON.parse(preferencesString || "{}");
  return {
    ...defaultPreferences,
    ...preferences,
  };
}

function setStorage(type: string, value: StorageInterface) {
  if (typeof window === "undefined") return;
  localStorage.setItem(type, JSON.stringify(value));
}

class Storage {
  static init() {
    const preferences = getStorage();
    setStorage(PREFERENCES, preferences);
  }

  static getIsHideLeftPanel() {
    const { isHideLeftPanel } = getStorage();
    return isHideLeftPanel || false;
  }

  static setIsHideLeftPanel(value: boolean) {
    const preferences = getStorage();
    preferences.isHideLeftPanel = value;
    setStorage(PREFERENCES, preferences);
  }

  static getHistorySearch(): THistorySearch[] {
    const { searchHistories } = getStorage();
    return searchHistories || [];
  }

  static setHistorySearch(historySearch: THistorySearch[]) {
    const preferences = getStorage();
    preferences.searchHistories = historySearch;
    setStorage(PREFERENCES, preferences);
  }

  static getUserSettings() {
    const { userSettings } = getStorage();
    return userSettings || {};
  }

  static setUserSettings(key: string, value: any) {
    const preferences = getStorage();
    (preferences.userSettings as any)[key] = value;
    setStorage(PREFERENCES, preferences);
  }

  static clearHistorySearch() {
    const preferences = getStorage();
    delete preferences.searchHistories;
    setStorage(PREFERENCES, preferences);
  }

  static getFavourites(network: string): string[] {
    const { favourites } = getStorage();
    return favourites?.[network] || [];
  }

  static addFavourite(network: string, item: string) {
    const preferences = getStorage();
    if (!preferences.favourites) {
      preferences.favourites = {};
    }
    if (!preferences.favourites[network]) {
      preferences.favourites[network] = [];
    }
    if (!preferences.favourites[network].includes(item)) {
      preferences.favourites[network].push(item);
      setStorage(PREFERENCES, preferences);
    }
  }

  static removeFavourite(network: string, item: string) {
    const preferences = getStorage();
    if (preferences.favourites && preferences.favourites[network]) {
      preferences.favourites[network] = preferences.favourites[network].filter(
        (fav: string): boolean => fav !== item
      );
      setStorage(PREFERENCES, preferences);
    }
  }

  static setReferralCode(referralCode: string) {
    const preferences = getStorage();
    preferences.referralCode = referralCode;
    setStorage(PREFERENCES, preferences);
  }

  static getReferralCode() {
    const { referralCode } = getStorage();
    return referralCode;
  }

  static getAccessToken(): string | undefined {
    const { accessToken } = getStorage();
    return accessToken;
  }

  static setAccessToken(accessToken: string) {
    const preferences = getStorage();
    preferences.accessToken = accessToken;
    setStorage(PREFERENCES, preferences);
  }

  static clearAccessToken() {
    const preferences = getStorage();
    delete preferences.accessToken;
    setStorage(PREFERENCES, preferences);
  }

  static getNetwork(): NETWORKS {
    const { network } = getStorage();
    return network || NETWORKS.SUI; // Default to SUI network
  }

  static setNetwork(network: string) {
    const preferences = getStorage();
    preferences.network = network;
    setStorage(PREFERENCES, preferences);
  }

  static clearNetwork() {
    const preferences = getStorage();
    delete preferences.network;
    setStorage(PREFERENCES, preferences);
  }

  static getLoginMethod(): string | undefined {
    const { loginMethod } = getStorage();
    return loginMethod;
  }

  static setLoginMethod(loginMethod: string) {
    const preferences = getStorage();
    preferences.loginMethod = loginMethod;
    setStorage(PREFERENCES, preferences);
  }

  static clearLoginMethod() {
    const preferences = getStorage();
    delete preferences.loginMethod;
    setStorage(PREFERENCES, preferences);
  }

  static getWalletAddresses(): string[] | undefined {
    const { walletAddresses } = getStorage();
    return walletAddresses;
  }

  static setWalletAddresses(walletAddresses: string[]) {
    const preferences = getStorage();
    preferences.walletAddresses = walletAddresses;
    setStorage(PREFERENCES, preferences);
  }

  static clearWalletAddresses() {
    const preferences = getStorage();
    delete preferences.walletAddresses;
    setStorage(PREFERENCES, preferences);
  }

  static getOrderSettings(): TOrderSettings {
    const { orderSettings } = getStorage();
    return orderSettings;
  }

  static setOrderSettings(orderSettings: TOrderSettings) {
    const preferences = getStorage();
    preferences.orderSettings = orderSettings;
    setStorage(PREFERENCES, preferences);
  }
  static setSnipeSettings(snipeSettings: TSnipeSettings) {
    const preferences = getStorage();
    preferences.snipeSettings = snipeSettings;
    setStorage(PREFERENCES, preferences);
  }

  static getSnipeSettings() {
    const { snipeSettings } = getStorage();
    return snipeSettings;
  }

  static getIsHideOtherTokenForOpenOrder(): boolean {
    const preferences = getStorage() || {};
    return preferences.isHideOtherTokenForOpenOrder || false;
  }

  static setIsHideOtherTokenForOpenOrder(value: boolean): void {
    const preferences = getStorage() || {};
    preferences.isHideOtherTokenForOpenOrder = value;
    setStorage(PREFERENCES, preferences);
  }

  static getIsHideOtherTokenForPosition(): boolean {
    const preferences = getStorage() || {};
    return preferences.isHideOtherTokenForPosition || false;
  }

  static setIsHideOtherTokenForPosition(value: boolean): void {
    const preferences = getStorage() || {};
    preferences.isHideOtherTokenForPosition = value;
    setStorage(PREFERENCES, preferences);
  }

  static setPairSearch(type: string, value: any) {
    if (!type) return;
    const preferences = getStorage();
    if (!preferences.pairSearch) {
      preferences.pairSearch = {};
    }
    preferences.pairSearch[type as "newPairs" | "trending"] = value;
    setStorage(PREFERENCES, preferences);
  }

  static getPairSearch(type: string) {
    const { pairSearch } = getStorage();
    if (!pairSearch) {
      return {};
    }
    return pairSearch[type as "newPairs" | "trending"] || {};
  }

  static setMemeSearch(type: string, value: any) {
    if (!type) return;
    const preferences = getStorage();
    if (!preferences.memeSearch) {
      preferences.memeSearch = {};
    }
    preferences.memeSearch[type as "funzone_filter"] = value;
    setStorage(PREFERENCES, preferences);
  }

  static getMemeSearch(type: string) {
    const { memeSearch } = getStorage();
    if (!memeSearch) {
      return "";
    }

    return memeSearch[type as "funzone_filter"] || {};
  }

  static getTrendingRankBy() {
    const { pairSearch } = getStorage();
    return pairSearch?.trendingRankingBy || "24h";
  }

  static setTrendingRankBy(value: string) {
    const preferences = getStorage();
    preferences.pairSearch.trendingRankingBy = value || "24h";
    setStorage(PREFERENCES, preferences);
  }

  static clearPairSearch() {
    const preferences = getStorage();
    if (preferences.pairSearch) {
      preferences.pairSearch.pair_filter = {} as TPairSearch;
    }
    setStorage(PREFERENCES, preferences);
  }

  static getMultiChart() {
    const { multiChart } = getStorage();
    return multiChart;
  }

  static setDataMultiChart(dataMultiChart: TMultiChart[]) {
    const preferences = getStorage();
    preferences.multiChart.data = dataMultiChart;
    setStorage(PREFERENCES, preferences);
  }
  static setColumnsMultiChart(columns: number) {
    const preferences = getStorage();
    preferences.multiChart.columns = columns;
    setStorage(PREFERENCES, preferences);
  }

  static setHeightMultiChart(height: number) {
    const preferences = getStorage();
    preferences.multiChart.height = height;
    setStorage(PREFERENCES, preferences);
  }
  static setDontShowIssuesDetectedAgain(value: any) {
    const preferences = getStorage();
    preferences.dontShowIssuesDetectedAgain = {
      ...preferences.dontShowIssuesDetectedAgain,
      [value.pairId]: value.dontShowAgain,
    };
    setStorage(PREFERENCES, preferences);
  }

  static getDontShowIssuesDetectedAgain() {
    const { dontShowIssuesDetectedAgain } = getStorage();
    return dontShowIssuesDetectedAgain || {};
  }

  static setRedirectAfterLogin(redirectAfterLogin: string) {
    const preferences = getStorage();
    preferences.redirectAfterLogin = redirectAfterLogin;
    setStorage(PREFERENCES, preferences);
  }

  static getRedirectAfterLogin() {
    const { redirectAfterLogin } = getStorage();
    return redirectAfterLogin;
  }

  static clearRedirectAfterLogin() {
    const preferences = getStorage();
    if (preferences.redirectAfterLogin) {
      delete preferences.redirectAfterLogin;
      setStorage(PREFERENCES, preferences);
    }
  }

  static setIsHideInstallApp(isHideInstallApp: boolean) {
    const preferences = getStorage();
    preferences.isHideInstallApp = isHideInstallApp;
    setStorage(PREFERENCES, preferences);
  }

  static getIsHideInstallApp() {
    const { isHideInstallApp } = getStorage();
    return isHideInstallApp;
  }
}

export default Storage;
