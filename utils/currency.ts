import { NETWORKS, getNativeTokenForNetwork } from "@/utils/contants";
import { getNetworkConfig } from "@/app/providers/networkChains";

export interface CurrencyItem {
  address: string;
  symbol: string;
  decimals: number;
  icon?: React.ReactNode;
}

export const getCurrencyListByNetwork = (network: NETWORKS): CurrencyItem[] => {
  const currencies: CurrencyItem[] = [];
  const nativeToken = getNativeTokenForNetwork(network);
  const networkConfig = getNetworkConfig(network);

  currencies.push({
    address: nativeToken.full,
    symbol: nativeToken.symbol,
    decimals: nativeToken.decimals,
  });

  if (networkConfig?.chainType === "ethereum") {
    currencies.push({
      address: nativeToken.wrap,
      symbol: `W${nativeToken.symbol}`,
      decimals: nativeToken.decimals,
    });
  }

  return currencies;
};
