import { OrderFormType } from "@/enums";

export const MOONBAGS_API_URL = "https://api2.moonbags.io/api/v1";

export const CATEGORY_MARKER = {
  SHRIMP: "shrimp",
  WHALE: "whale",
  DOLPHIN: "dolphin",
  PLANKTON: "plankton",
  FISH: "fish",
};

export enum NETWORKS {
  SUI = "sui",
  HYPEREVM = "hyperevm",
  SOMNIA = "somnia",
  PLASMA = "plasma",
}

export const DATE_TYPE = {
  DATE: "Date",
  AGE: "Age",
};

export const UNIT_TYPE = {
  USD: "USD",
  TOKEN: "TOKEN",
};

export const SUI_TOKEN_ADDRESS_FULL =
  "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI";
export const SUI_TOKEN_ADDRESS_SHORT = "0x2::sui::SUI";
export const SUI_DECIMALS = 9;

export const NETWORK_NATIVE_TOKENS = {
  sui: {
    full: "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI",
    short: "0x2::sui::SUI",
    wrap: "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI",
    decimals: 9,
    symbol: "SUI",
  },
  hyperevm: {
    full: "0x0000000000000000000000000000000000000000",
    short: "0x0000000000000000000000000000000000000000",
    wrap: "0x5555555555555555555555555555555555555555",
    decimals: 18,
    symbol: "HYPE",
  },
  somnia: {
    full: "0x0000000000000000000000000000000000000000",
    short: "0x0000000000000000000000000000000000000000",
    wrap: "0x0000000000000000000000000000000000000000",
    decimals: 18,
    symbol: "SOMI",
  },
  plasma: {
    full: "0x0000000000000000000000000000000000000000",
    short: "0x0000000000000000000000000000000000000000",
    wrap: "0x0000000000000000000000000000000000000000",
    decimals: 18,
    symbol: "XPL",
  },
} as const;

// Sui-specific USDC (legacy)
export const USDC_ADDRESS =
  "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC";
export const USDC_DECIMALS = 6;

export const LOCK_TRUST_ME_BRO = "246153846153893490";
export const LOCK_TRUST_ME_3000 = "505263157894803323";
export const LOW_LIQUIDITY = 5000;
export const LOW_LIQUIDITY_FUNZONE = 1000;

// Legacy SUI token metadata (use getNativeTokenMetadata from networkHelper instead)
export const SUI_TOKEN_METADATA = {
  address: SUI_TOKEN_ADDRESS_SHORT,
  decimals: SUI_DECIMALS,
  symbol: "SUI",
  name: "SUI",
};

export const getNativeTokenForNetwork = (network: string) => {
  return (
    NETWORK_NATIVE_TOKENS[network as keyof typeof NETWORK_NATIVE_TOKENS] ||
    NETWORK_NATIVE_TOKENS.sui
  );
};

export const OPTIONS_ORDER_TYPE = [
  {
    name: "Market",
    value: OrderFormType.MARKET,
  },
  {
    name: "Limit",
    value: OrderFormType.LIMIT,
  },
  {
    name: "DCA",
    value: OrderFormType.DCA,
  },
  {
    name: "Migration",
    value: OrderFormType.MIGRATION,
  },
];

export const LOGIN_METHODS = {
  TELEGRAM: "telegram",
  PRIVY: "privy",
  WALLET: "wallet",
};
