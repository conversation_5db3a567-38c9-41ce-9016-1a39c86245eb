import { SuiIcon, SuiIconBase, HypeIcon, PlasmaIcon } from "@/assets/icons";
import React from "react";
import { NETWORKS } from "@/utils/contants";
import Image from "next/image";

const SomiIcon = ({ className }: { className?: string }) => (
  <div
    className={`flex items-center justify-center rounded-full bg-orange-500 text-xs font-bold text-white ${className}`}
  >
    S
  </div>
);

export const AppLogoNetwork = ({
  network,
  className,
  isBase = false,
}: {
  network: string;
  className?: string;
  isBase?: boolean;
}) => {
  if (network === NETWORKS.SUI) {
    if (isBase) {
      return <SuiIconBase className={className} />;
    }
    return <SuiIcon className={className} />;
  }

  if (network === NETWORKS.HYPEREVM) {
    return (
      <HypeIcon
        className={
          "flex h-[14px] w-[14px] items-center justify-center rounded-full"
        }
      />
    );
  }

  if (network === NETWORKS.PLASMA) {
    return (
      <PlasmaIcon
        className={
          "flex h-[14px] w-[14px] items-center justify-center rounded-full"
        }
      />
    );
  }

  if (network === NETWORKS.SOMNIA) {
    return <SomiIcon className={className} />;
  }

  return <div />;
};
