"use client";
import clsx from "clsx";
import * as React from "react";
import { useEffect, useRef, useState } from "react";
import { NumericFormat } from "react-number-format";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { ChevronDownIcon, DefaultAvatar, WalletIcon } from "@/assets/icons";
import { AppPopover } from "@/components";
import AvatarSelectPair from "@/components/Snipe/AvatarSelectPair";
import {
  DEXES_CAN_BUY_WITH_SUAI,
  SUI_ADDRESS,
} from "@/components/Snipe/constant";
import {
  optionsTargetPair,
  getTokenNameFromTokenAddress,
} from "@/components/Snipe/helper";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { ModalBottomMobile } from "@/modals";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import {
  setIsShowModalAddWallet,
  setIsShowModalEnableTradingPrivy,
} from "@/store/metadata.store";
import { DEXS, getDexLogoUrl, getDexName } from "@/utils/dex";
import { formatNumber, formatNumberWithCommas } from "@/utils/format";
import { AmountForm } from "../../../components/Snipe/amount-form";
import { FooterForm } from "../../../components/Snipe/footer-form";
import { SettingsForm } from "../../../components/Snipe/settings-form";
import { WalletSelection } from "../../../components/Snipe/wallet-selection";
import DrawerHoneypotDetected from "./honeypot-detect";
import DexSelection from "@/components/Snipe/dex-selection";
import { IShowModalSnipe, ISnipeForm } from "@/components/Snipe/type";
import Storage from "@/libs/storage";
import Image from "next/image";
import { ModalAutoSell } from "../../OrderForm/buy-order/AutoSellForm";
import { usePrivyTradingEnablement } from "../../../hooks/usePrivyTradingEnablement";
import { useTradingWallet } from "@/hooks/useTradingWallet";

const SnipeFormPart = () => {
  const dispatch = useDispatch();
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const minLiquidityRef = useRef<any>(null);
  const { accessToken, network, wallets, listDex } = useSelector(
    (state: RootState) => ({
      accessToken: state.user.accessToken,
      network: state.user.network,
      wallets: state.user.wallets,
      listDex: state.metadata.dexesSnipe,
    })
  );

  const [showModal, setShowModal] = useState<IShowModalSnipe>({
    settings: false,
    selectWallet: false,
    modalWalletSelection: false,
    modalSettingsOrder: false,
    options: false,
    targetPair: false,
    honeypotDetected: false,
  });

  const initialSnipeForm: ISnipeForm = {
    buyAmount: "",
    minLiquidityByTokenQuote: "",
    targetTokenAddress: "",
    targetTokenQuoteAddress: SUI_ADDRESS,
    buyByToken: SUI_ADDRESS,
  };

  const snipeSettings = Storage.getSnipeSettings();
  const [snipeForm, setSnipeForm] = useState<ISnipeForm>(initialSnipeForm);

  // auto sell
  const [isSettingAutoSell, setIsSettingAutoSell] = useState<boolean>(
    snipeSettings?.snipeLiquidity?.autoSell?.isSettingAutoSell || false
  );
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [receiveToken, setReceiveToken] = useState<string | null>(
    snipeSettings?.snipeLiquidity?.autoSell?.receiveToken || null
  );
  const [triggersOrder, setTriggersOrder] = useState<any[]>(
    snipeSettings?.snipeLiquidity?.autoSell?.triggers || [
      {
        priceChangePercent: 10,
        sellPercent: 50,
      },
      {
        priceChangePercent: -10,
        sellPercent: 50,
      },
    ]
  );

  const { isPrivyUser, isTradingEnabled } = usePrivyTradingEnablement();
  const settingsSnipeOrder = useSelector(
    (state: RootState) => state.user.settingsSnipeOrder
  );

  const allDexes = listDex?.map((item) => item.name);
  const [dexSelected, setDexSelected] = useState<string>(allDexes[0]);
  useEffect(() => {
    setDexSelected(allDexes[0]);
  }, [listDex]);
  const objDexSelected = listDex?.find((item) => item?.name === dexSelected);

  const { activeTradingWallets: activeSnipeWallets, activeTotalQuoteBalance } =
    useTradingWallet(SUI_ADDRESS, snipeForm.buyByToken);

  const snipe = async () => {
    try {
      let autoSellSettings = null as any;

      if (isSettingAutoSell) {
        if (!triggersOrder?.length) {
          toastError("Error", "Please setting for auto sell");
          return;
        }

        autoSellSettings = {
          isActive: true,
          receiveToken:
            snipeForm?.targetTokenQuoteAddress === SUI_ADDRESS
              ? null
              : receiveToken,
          triggers: triggersOrder,
        };
      }

      const res = await rf
        .getRequest("SnipeDexRequest")
        .createSnipeDex(network, {
          autoSellSettings,
          buyAmount: +snipeForm.buyAmount,
          buyByToken: snipeForm.buyByToken,
          minLiquidityByTokenQuote: Number(
            snipeForm?.minLiquidityByTokenQuote || 0
          ),
          slippage: +settingsSnipeOrder.slippage || 0,
          gasPrice: +settingsSnipeOrder.gasPrice || 0,
          tipAmount: +settingsSnipeOrder.tipAmount || 0,
          targetDex: objDexSelected?.dex,
          targetTokenAddress: snipeForm.targetTokenAddress,
          targetTokenQuoteAddress: snipeForm.targetTokenQuoteAddress,
          userWalletAddresses: activeSnipeWallets.map(
            (wallet) => wallet.address
          ),
        });
      const allFailed = res.every((snipe: any) => !snipe.isSuccess);

      if (allFailed) {
        toastError(
          "Error",
          "This snipe listing setting existed, only update or delete"
        );
        return;
      }
      AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST);
      const walletsFailed = res.filter((snipe: any) => !snipe.isSuccess);
      if (walletsFailed.length > 0) {
        toastError(
          "Error",
          `Wallet ${walletsFailed
            .map((wallet: any) => wallet.userWalletAddress)
            .join(", ")} dex listing setting existed, only update or delete`
        );
        return;
      }

      setSnipeForm({
        ...snipeForm,
        targetTokenAddress: "",
        minLiquidityByTokenQuote: "",
        buyAmount: "",
      });
      toastSuccess("Success", "Snipe order created successfully!");
    } catch (error: any) {
      toastError("Error", error.message || "Something went wrong!");
    }
  };

  const createSnipeOrder = async () => {
    if (!snipeForm.targetTokenAddress) {
      toastError("Error", "Please enter target token address");
      return;
    }

    if (!snipeForm.buyAmount) {
      toastError("Error", "Please enter amount");
      return;
    }

    if (isPrivyUser && !isTradingEnabled) {
      dispatch(setIsShowModalEnableTradingPrivy({ isShow: true }));
      return;
    }

    try {
      const isHoneypot = await rf
        .getRequest("TokenRequest")
        .checkHoneypot(network, snipeForm.targetTokenAddress);
      if (isHoneypot?.isHoneyPot) {
        setShowModal({
          ...showModal,
          honeypotDetected: true,
        });
        return;
      }
    } catch (error: any) {
      toastError("Error", error.message || "Something went wrong!");
    }
    snipe();
  };
  const onShowSettings = () => {
    if (!accessToken) return;
    if (isTabletOrMobile) {
      setShowModal({
        ...showModal,
        modalSettingsOrder: true,
      });
      return;
    }
    setShowModal({
      ...showModal,
      settings: true,
    });
  };

  const handleFocusInputAmount = () => {
    const formattedAmount = formatNumberWithCommas(
      snipeForm?.minLiquidityByTokenQuote || 0
    );
    minLiquidityRef.current?.setSelectionRange(
      formattedAmount.length,
      formattedAmount.length
    );
  };
  const onSelectDex = (dex: string) => {
    setShowModal({
      ...showModal,
      options: false,
    });
    setDexSelected(dex);
    if (!DEXES_CAN_BUY_WITH_SUAI.includes(dex)) {
      setSnipeForm((prevForm) => ({
        ...prevForm,
        buyByToken: SUI_ADDRESS,
        targetTokenQuoteAddress: SUI_ADDRESS,
      }));
    }
  };

  const onSelectTargetPair = (targetTokenQuoteAddress: string) => {
    setShowModal({
      ...showModal,
      targetPair: false,
    });
    setSnipeForm((prev) => ({
      ...prev,
      targetTokenQuoteAddress,
      buyByToken: targetTokenQuoteAddress,
    }));
  };

  const toggleSettingAutoSell = () => {
    if (!accessToken) return;

    if (isSettingAutoSell) {
      setIsSettingAutoSell(false);
      Storage.setSnipeSettings({
        ...snipeSettings,
        snipeLiquidity: {
          ...snipeSettings?.snipeLiquidity,
          autoSell: {
            ...snipeSettings?.snipeLiquidity?.autoSell,
            isSettingAutoSell: false,
          },
        },
      });
      return;
    }
    setIsSettingAutoSell(true);
    setIsShowSettingAutoSell(true);

    Storage.setSnipeSettings({
      ...snipeSettings,
      snipeLiquidity: {
        ...snipeSettings?.snipeLiquidity,
        autoSell: {
          ...snipeSettings?.snipeLiquidity?.autoSell,
          isSettingAutoSell: true,
        },
      },
    });
  };

  const onOpenSettingAutoSell = () => {
    if (!accessToken) return;
    setIsShowSettingAutoSell(true);
  };

  const _renderContent = () => {
    if (showModal.settings) {
      return (
        <SettingsForm
          onCloseSettings={() =>
            setShowModal({ ...showModal, settings: false })
          }
        />
      );
    }
    if (showModal.selectWallet) {
      return (
        <WalletSelection
          snipeForm={snipeForm}
          onCloseWalletSettings={() =>
            setShowModal({ ...showModal, selectWallet: false })
          }
        />
      );
    }

    return (
      <div className="h-full">
        <div className="border-white-100 border-b pb-3">
          <div className="body-sm-regular-12 text-white-500 mb-[8px] flex items-center justify-between">
            <div>Choose Dex</div>
          </div>

          <AppPopover
            customClassWrapper="w-full"
            isOpen={showModal.options}
            onToggle={() => {
              setShowModal({
                ...showModal,
                options: !showModal.options,
              });
            }}
            onClose={() => setShowModal({ ...showModal, options: false })}
            trigger={
              <div
                className={`bg-brand-900 border-brand-800 body-sm-regular-12 flex h-[36px] w-full cursor-pointer items-center
                  justify-between gap-2 rounded-[6px] border px-[8px] capitalize`}
              >
                <div className="flex items-center gap-2">
                  {objDexSelected?.dex &&
                    DEXS[objDexSelected.dex as keyof typeof DEXS] && (
                      <Image
                        src={getDexLogoUrl(
                          objDexSelected.dex as keyof typeof DEXS
                        )}
                        alt={getDexName(
                          objDexSelected.dex as keyof typeof DEXS
                        )}
                        className="h-[16px] w-[16px] rounded-full"
                        width={16}
                        height={16}
                        unoptimized
                      />
                    )}

                  {dexSelected}
                </div>
                <ChevronDownIcon
                  className={`duration-400 h-4 w-4 ${
                    showModal.options ? "rotate-[180deg]" : ""
                  }`}
                />
              </div>
            }
            content={
              <DexSelection
                dexSelected={dexSelected}
                onSelectDex={onSelectDex}
                listDex={listDex}
              />
            }
            position="left"
          />
        </div>
        <div className="border-white-50 bg-black-900 my-3 flex h-full max-h-[34px] items-center gap-2 rounded border px-1 pl-1 pr-2">
          <div className="text-white-800 border-white-50 border-r pl-[2px] pr-1 text-[12px] font-normal leading-[18px]">
            Token address
          </div>
          <input
            value={snipeForm.targetTokenAddress}
            onChange={(e) =>
              setSnipeForm({
                ...snipeForm,
                targetTokenAddress: e.target.value,
              })
            }
            type="text"
            className="text-white-1000 placeholder:text-white-200 bg-black-900 flex-1 text-[12px] font-normal leading-[18px] focus:outline-none"
            placeholder="Add token address"
          />
        </div>

        {optionsTargetPair(dexSelected).length > 1 && (
          <div>
            <div className="body-sm-regular-12 text-white-500 mb-[8px] flex items-center justify-between">
              <div>Select Target Pair</div>
            </div>

            <AppPopover
              customClassWrapper="w-full"
              isOpen={showModal.targetPair}
              onToggle={() => {
                setShowModal({
                  ...showModal,
                  targetPair: !showModal.targetPair,
                });
              }}
              onClose={() => setShowModal({ ...showModal, targetPair: false })}
              trigger={
                <div
                  className={`bg-neutral-alpha-50 body-sm-regular-12 flex h-[36px] w-full cursor-pointer items-center justify-between
                  gap-2 rounded-[6px] border-none px-[8px] capitalize`}
                >
                  <div className="flex items-center gap-2">
                    {`Token/${getTokenNameFromTokenAddress(
                      snipeForm.targetTokenQuoteAddress
                    )}`}
                  </div>
                  <ChevronDownIcon
                    className={`duration-400 h-4 w-4 ${
                      showModal.targetPair ? "rotate-[180deg]" : ""
                    }`}
                  />
                </div>
              }
              content={
                <div className="flex w-full flex-col flex-wrap gap-2 rounded-[8px] bg-[#212224] p-[4px]">
                  {optionsTargetPair(dexSelected).map((item, index) => {
                    return (
                      <div
                        onClick={() => onSelectTargetPair(item?.value)}
                        key={index}
                        className={`${
                          snipeForm.targetTokenQuoteAddress === item?.value
                            ? "border-white-100 bg-white-100"
                            : "border-white-50"
                        }
                      body-sm-regular-12 hover:border-white-100 flex h-[36px] cursor-pointer items-center gap-2 rounded-[6px] border px-[8px]`}
                      >
                        <span>{item?.label}</span>
                      </div>
                    );
                  })}
                </div>
              }
              position="left"
            />
          </div>
        )}

        <div className="border-white-50 bg-black-900 my-3 flex h-full max-h-[34px] items-center gap-2 rounded border px-1 pl-1 pr-2">
          <div className="text-white-800 border-white-50 flex-shrink-0 border-r pl-[2px] pr-1 text-[12px] font-normal leading-[18px]">
            Min Liquidity{" "}
            {getTokenNameFromTokenAddress(snipeForm.targetTokenQuoteAddress)}
          </div>

          <NumericFormat
            getInputRef={minLiquidityRef}
            value={snipeForm.minLiquidityByTokenQuote ?? ""}
            onFocus={handleFocusInputAmount}
            allowLeadingZeros
            allowNegative={false}
            placeholder={`Min Liquidity ${getTokenNameFromTokenAddress(
              snipeForm.targetTokenQuoteAddress
            )}`}
            thousandSeparator=","
            suffix={` ${getTokenNameFromTokenAddress(
              snipeForm.targetTokenQuoteAddress
            )}`}
            className="body-md-semibold-14 text-white-1000 placeholder:text-white-200 bg-black-900 w-full flex-1
              bg-transparent text-left text-[12px] font-normal leading-[18px] outline-none focus:outline-none"
            decimalScale={8}
            onValueChange={({ floatValue }) => {
              setSnipeForm({
                ...snipeForm,
                minLiquidityByTokenQuote: floatValue?.toString() ?? "",
              });
            }}
          />
        </div>

        {!!wallets.length ? (
          <div
            onClick={() => setShowModal({ ...showModal, selectWallet: true })}
            className="bg-neutral-alpha-50 flex cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
          >
            <div className="flex items-center gap-2">
              <div className="tablet:bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                <WalletIcon />
              </div>
              <div className="flex gap-2">
                <div className="body-sm-semibold-12">Wallet</div>
                <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                  {activeSnipeWallets.length} selected
                </div>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <div className="flex items-center gap-1">
                <div className="body-sm-regular-12">
                  {formatNumber(activeTotalQuoteBalance, 4)}
                </div>

                <AvatarSelectPair
                  coin={getTokenNameFromTokenAddress(snipeForm.buyByToken)}
                />
              </div>

              <div>
                <ChevronDownIcon className="text-neutral-alpha-500 h-[16px] w-[16px] rotate-[-90deg]" />
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] p-[4px]">
            <div className="flex items-center gap-2">
              <div className="bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                <WalletIcon />
              </div>
              <div className="flex gap-2">
                <div className="body-sm-semibold-12">Wallet</div>
              </div>
            </div>
            {accessToken && (
              <div
                className="action-xs-medium-12 text-brand-500 cursor-pointer"
                onClick={() => {
                  dispatch(setIsShowModalAddWallet({ isShow: true }));
                }}
              >
                Add Wallet
              </div>
            )}
          </div>
        )}

        <div>
          <AmountForm setSnipeForm={setSnipeForm} snipeForm={snipeForm} />
        </div>

        <FooterForm
          snipeForm={snipeForm}
          createOrder={createSnipeOrder}
          balance={activeTotalQuoteBalance}
          onShowSettings={onShowSettings}
          toggleSetAutoSell={toggleSettingAutoSell}
          autoSell={isSettingAutoSell}
          onShowSettingAutoSell={onOpenSettingAutoSell}
        />

        <DrawerHoneypotDetected
          isOpenDrawerHoneypotDetected={showModal.honeypotDetected!}
          setIsOpenDrawerHoneypotDetected={(isOpen) =>
            setShowModal({
              ...showModal,
              honeypotDetected: isOpen,
            })
          }
          snipe={snipe}
        />

        <ModalAutoSell
          isShowReceiveSui={snipeForm.targetTokenQuoteAddress !== SUI_ADDRESS}
          onClose={() => setIsShowSettingAutoSell(false)}
          isOpen={isShowSettingAutoSell}
          setReceiveToken={setReceiveToken}
          receiveToken={receiveToken}
          setTriggersOrder={setTriggersOrder}
          triggersOrder={triggersOrder}
          typeSnipe="liquidity"
        />
      </div>
    );
  };

  return (
    <div
      className={clsx(
        "bg-neutral-alpha-50 tablet:pr-[8px] border-neutral-alpha-50 tablet:overflow-hidden flex h-full w-full overflow-auto border-b px-[8px] py-[12px]",
        showModal.selectWallet ? "pr-2" : "pr-2"
      )}
    >
      <div className="w-full">{_renderContent()}</div>

      {showModal.modalWalletSelection && (
        <ModalBottomMobile
          isOpen={showModal.modalWalletSelection}
          onClose={() =>
            setShowModal({ ...showModal, modalWalletSelection: false })
          }
        >
          <div className="w-full p-[16px]">
            <WalletSelection
              snipeForm={snipeForm}
              onCloseWalletSettings={() =>
                setShowModal({ ...showModal, modalWalletSelection: false })
              }
            />
          </div>
        </ModalBottomMobile>
      )}

      {showModal.modalSettingsOrder && (
        <ModalBottomMobile
          isOpen={showModal.modalSettingsOrder}
          onClose={() =>
            setShowModal({ ...showModal, modalSettingsOrder: false })
          }
        >
          <div className="w-full p-[16px]">
            <SettingsForm
              onCloseSettings={() =>
                setShowModal({ ...showModal, modalSettingsOrder: false })
              }
            />
          </div>
        </ModalBottomMobile>
      )}
    </div>
  );
};
export default SnipeFormPart;
