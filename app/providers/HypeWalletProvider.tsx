"use client";

import { ReactNode } from "react";
import { PrivyProvider } from "@privy-io/react-auth";
import { hypeMainnet<PERSON>hain } from "./networkChains";
import config from "@/config";

interface HypeWalletProviderProps {
  children: ReactNode;
}

const HYPE_PRIVY_APP_ID =
  config?.privyConfig?.appId || "cmf3ual0i0193kv0c0ed61pqu";

export const HypeWalletProvider = ({ children }: HypeWalletProviderProps) => {
  return (
    <PrivyProvider
      appId={HYPE_PRIVY_APP_ID}
      config={{
        loginMethods: ["wallet", "email", "google", "twitter"],
        embeddedWallets: {
          ethereum: {
            createOnLogin: "users-without-wallets",
          },
        },
        appearance: {
          walletChainType: "ethereum-only",
          theme: "dark",
          accentColor: "#676FFF",
        },
        defaultChain: hypeMain<PERSON><PERSON>hain,
        supportedChains: [hypeMainnet<PERSON>hain],
      }}
    >
      {children}
    </PrivyProvider>
  );
};
