"use client";

import { ReactNode } from "react";
import { PrivyProvider } from "@privy-io/react-auth";
import { somniaMainnetChain } from "./networkChains";
import config from "@/config";

interface SomniaWalletProviderProps {
  children: ReactNode;
}

const SOMNIA_PRIVY_APP_ID =
  config?.privyConfig?.appId || "cmcvqsfzm0165jl0ms1i81h70";

export const SomniaWalletProvider = ({
  children,
}: SomniaWalletProviderProps) => {
  return (
    <PrivyProvider
      appId={SOMNIA_PRIVY_APP_ID}
      config={{
        loginMethods: ["wallet", "email", "google", "twitter"],
        embeddedWallets: {
          ethereum: {
            createOnLogin: "users-without-wallets",
          },
        },
        appearance: {
          walletChainType: "ethereum-only",
          theme: "dark",
          accentColor: "#676FFF",
        },
        defaultChain: somniaMainnet<PERSON>hain,
        supportedChains: [somniaMainnet<PERSON>hain],
      }}
    >
      {children}
    </PrivyProvider>
  );
};
