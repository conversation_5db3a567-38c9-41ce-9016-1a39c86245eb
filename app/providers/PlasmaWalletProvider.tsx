"use client";

import { ReactNode } from "react";
import { PrivyProvider } from "@privy-io/react-auth";
import { plasmaMainnetChain } from "./networkChains";
import config from "@/config";

interface PlasmaWalletProviderProps {
  children: ReactNode;
}

const PLASMA_PRIVY_APP_ID =
  config?.privyConfig?.appId || "cmf3ual0i0193kv0c0ed61pqu";

export const PlasmaWalletProvider = ({
  children,
}: PlasmaWalletProviderProps) => {
  return (
    <PrivyProvider
      appId={PLASMA_PRIVY_APP_ID}
      config={{
        loginMethods: ["wallet", "email", "google", "twitter"],
        embeddedWallets: {
          ethereum: {
            createOnLogin: "users-without-wallets",
          },
        },
        appearance: {
          walletChainType: "ethereum-only",
          theme: "dark",
          accentColor: "#676FFF",
        },
        defaultChain: plasmaMainnet<PERSON>hain,
        supportedChains: [plasmaMainnetChain],
      }}
    >
      {children}
    </PrivyProvider>
  );
};
