"use client";

import React, { useState } from "react";
import { CheckedIcon, CheckboxIcon } from "@/assets/icons";
import config from "@/config";
import <PERSON><PERSON> from "lottie-react";
import { BannerAnimation } from "@/assets/animations";
import { useMediaQuery } from "react-responsive";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { setIsShowModalConnectTelegram } from "@/store/metadata.store";
import { AppDispatch } from "@/store/index";
import { ConnectButton, useCurrentAccount, useWallets } from "@mysten/dapp-kit";

const BgBanner = () => {
  return <Lottie animationData={BannerAnimation} loop={true} />;
};

export const Banner = () => {
  const [isAgree, setIsAgree] = useState<boolean>(true);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );
  const dispatch = useDispatch<AppDispatch>();
  const currentAccount = useCurrentAccount();
  const wallets = useWallets();

  const onConnectTelegram = () => {
    dispatch(setIsShowModalConnectTelegram({ isShow: true }));
  };

  return (
    <div>
      <div
        className={`tablet:mb-[40px] tablet:h-[757px] relative h-[550px] w-full max-w-[1440px] overflow-hidden max-[460px]:h-[426px] ${
          !isHideInstallApp
            ? "aspect-[999/1140] md:h-[700px]"
            : "aspect-[999/1300] md:h-[700px]"
        }  tablet:aspect-auto tablet:bg-[url('/images/home-page/bg-banner.png')] mx-auto bg-[url('/images/home-page/bg-banner-mobile.png')] bg-cover bg-bottom bg-no-repeat`}
      >
        {!isMobile && (
          <div
            className="absolute bottom-0 left-1/2 z-[1] w-full"
            style={{ transform: "translateX(-50%)" }}
          >
            <BgBanner />
          </div>
        )}
        <div
          className={` ${
            !isHideInstallApp
              ? "pt-[50px] md:pt-[94px]"
              : "pt-[48px] md:pt-[74px]"
          } relative z-[2] mb-[80px]`}
        >
          <div className="flex flex-col items-center gap-2 px-4 md:gap-3">
            <div className="bg-gradient-text tablet:text-[48px] tablet:max-w-[743px] tablet:pt-[88px] mx-auto max-w-[320px] pt-[32px] text-center text-[24px] font-medium leading-[1.2]">
              Trade, snipe, copy trade on SUI at the speed of light
            </div>
            <div className="bg-gradient-text-2 tablet:body-md-regular-14 body-sm-regular-12">
              Connect to start trading in less than 30s
            </div>
            <div
              onClick={onConnectTelegram}
              className="hover:bg-white-100 body-md-medium-14 bg-black-700 flex w-[250px] cursor-pointer items-center justify-center gap-2 rounded-[8px] bg-[url('/images/home-page/bg-btn.png')] bg-cover bg-no-repeat px-4 py-[10px]"
            >
              Connect
            </div>

            {!!wallets.length && !currentAccount?.address && (
              <ConnectButton
                className="!bg-transparent !p-0 !shadow-none"
                connectText={
                  <div className="body-md-medium-14 !text-white-1000 flex cursor-pointer items-center gap-2">
                    Or Trading With Wallet{" "}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M12.4697 5.46967C12.7626 5.17678 13.2374 5.17678 13.5303 5.46967L19.5303 11.4697C19.8232 11.7626 19.8232 12.2374 19.5303 12.5303L13.5303 18.5303C13.2374 18.8232 12.7626 18.8232 12.4697 18.5303C12.1768 18.2374 12.1768 17.7626 12.4697 17.4697L17.1893 12.75H5C4.58579 12.75 4.25 12.4142 4.25 12C4.25 11.5858 4.58579 11.25 5 11.25H17.1893L12.4697 6.53033C12.1768 6.23744 12.1768 5.76256 12.4697 5.46967Z"
                        fill="white"
                      />
                    </svg>
                  </div>
                }
              />
            )}

            <div
              onClick={() => setIsAgree(!isAgree)}
              className="tablet:body-md-regular-14 body-sm-regular-12 text-white-500 flex cursor-pointer items-center gap-2"
            >
              {isAgree ? (
                <CheckedIcon className="h-4 w-4" />
              ) : (
                <CheckboxIcon className="h-4 w-4" />
              )}
              Buy connecting, I agree to the{" "}
              <div className="bg-gradient-text-2 body-md-regular-14 flex gap-2">
                <a href={config.homePage.link.termsOfUse} target="_blank">
                  Terms
                </a>
                &
                <a href={config.homePage.link.policy} target="_blank">
                  Policy
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
