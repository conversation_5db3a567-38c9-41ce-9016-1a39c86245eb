{"title": "RaidenX", "appUrl": "https://raidenx.io/", "newOrderUrl": "", "endpoints": {"ws": "https://ws.raidenx.io", "commonApi": "https://api.raidenx.io/api/v1", "webhookApi": "https://api-webhook.raidenx.io/api/v1", "userApi": "https://api-users.raidenx.io/api/v1", "alertApi": "https://api-alerts.raidenx.io/api/v1", "referralApi": "https://referral-api.raidenx.io/api", "insightApi": "https://insight-api.raidenx.io", "externalApi": "https://api-external.raidenx.io"}, "privyConfig": {"appId": "cmf3ual0i0193kv0c0ed61pqu", "clientId": "client-WY6N6weHGb9qMifP2DnCLkMcWgucCDDSN9cp4JUk89YFY", "signerId": "ld1ktc2blevu3xksfilr4ifi"}, "network": "mainnet", "link_telegram": "https://t.me/RaidenXTradeBot", "linkSocial": {"twitter": "http://x.com/raidenxofficial", "telegram": "https://t.me/raidenx_io", "discord": "https://discord.gg/8uzHqBM6B2"}, "customerSupportUrl": "https://telegram.me/raidenx_cs", "developerUrl": "https://raidenx.io/developer", "treasuryAddress": "0x240620c61a515e15459a0cfa95bcf88527382bc87aef2c2491acff715619799b", "homePage": {"link": {"telegramPremiumSignal": "https://t.me/addlist/uJ6anarWWyEyMzA1", "buyBot": "https://t.me/raidenx", "airdrop": "https://raidenx.io/airdrop", "developer": "https://raidenx.io/developer", "userGuide": "https://docs.raidenx.io/docs/dca-order", "apiDocs": "https://public-api-docs.raidenx.io/docs/getting-started", "termsOfUse": "https://raidenx.io/docs/terms-of-use", "policy": "https://raidenx.io/docs/privacy-policy", "roadMap": "https://raidenx.canny.io/", "customerSupport": "https://t.me/raidenx_cs", "brandKit": "https://raidenx.io/media-kit"}}, "sponsorAddress": "0x34b2c6c1ef038334f2bc5142d0613c7b5f6ec5a67ae4a7cf37340adefdf2cfb6", "verifyToken": {"limited": {"price": 20}, "standard": {"price": 40}}, "minGasFee": 0.15, "raidenxFeeRate": 0.01, "raidenxFeeReceiverAddress": "0x0d66d7ac45c1d011fdd21c5d4d193913c8c40d5f1e1e62e33f72ffa0f2b86e2a", "networks": {"sui": {"network": "mainnet", "rpcUrl": "https://fullnode.mainnet.sui.io", "explorerUrl": "https://suivision.xyz", "treasuryAddress": "0x240620c61a515e15459a0cfa95bcf88527382bc87aef2c2491acff715619799b", "endpoints": {"domainNameApi": "https://api-suins.raidenx.io/api/v1", "walletApi": "https://api-user-wallets.raidenx.io/api/v1", "orderApi": "https://api-orders.raidenx.io/api/v1", "marketApi": ""}, "quoteTokens": [{"address": "******************************************000000000000000000000002::sui::SUI", "symbol": "SUI", "decimals": 9}]}, "hyperevm": {"network": "mainnet", "rpcUrl": "", "explorerUrl": "", "treasuryAddress": "", "endpoints": {"domainNameApi": "", "walletApi": "", "orderApi": "", "marketApi": ""}, "quoteTokens": [{"address": "******************************************", "symbol": "HYPE", "decimals": 18}]}, "somnia": {"network": "mainnet", "rpcUrl": "", "explorerUrl": "", "treasuryAddress": "", "endpoints": {"domainNameApi": "", "walletApi": "", "orderApi": "", "marketApi": ""}, "quoteTokens": [{"address": "******************************************", "symbol": "SOMNIA", "decimals": 18}]}, "plasma": {"network": "mainnet", "rpcUrl": "https://rpc.plasma.to", "explorerUrl": "https://plasmascan.to", "treasuryAddress": "", "endpoints": {"domainNameApi": "", "walletApi": "", "orderApi": "", "marketApi": ""}, "quoteTokens": [{"address": "******************************************", "symbol": "XPL", "decimals": 18}]}}}