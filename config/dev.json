{"title": "Dextrade", "appUrl": "https://uat-raidenx.vercel.app/", "newOrderUrl": "", "endpoints": {"ws": "wss://uat-ws-raidenx.sotalabs.io", "commonApi": "https://uat-api-raidenx.sotalabs.io/api/v1", "webhookApi": "https://uat-api-webhook-raidenx.sotalabs.io/api/v1", "userApi": "https://uat-api-users-raidenx.sotalabs.io/api/v1", "alertApi": "https://uat-api-alerts-raidenx.sotalabs.io/api/v1", "referralApi": "https://uat-api-points-raidenx.sotalabs.io/api", "insightApi": "https://uat-api-insight-raidenx.sotalabs.io", "externalApi": "https://uat-api-external-raidenx.sotalabs.io"}, "privyConfig": {"appId": "cmcx1ca72032ek80mm7e52086", "clientId": "client-WY6N6weHGb9qMifP2DnCLkMcWgucCDDSN9cp4JUk89YFY", "signerId": "htdpytx08wjk233gl1ce7eua"}, "network": "testnet", "explorerSuiUrl": "https://suivision.xyz", "link_telegram": "https://t.me/dextrade_test_bot", "linkSocial": {"twitter": "https://x.com/dextrade_io", "telegram": "https://t.me/dextrade_io", "discord": "https://discord.gg/8uzHqBM6B2"}, "customerSupportUrl": "https://telegram.me/raidenx_cs", "developerUrl": "http://developer.dextrade.bot/", "treasuryAddress": "0x0db7989b98d681455f424035e3f01c02e27f738fdd6634ef34dedf576a9d8cea", "homePage": {"link": {"telegramPremiumSignal": "https://t.me/addlist/uJ6anarWWyEyMzA1", "buyBot": "https://t.me/raidenx", "airdrop": "https://dextrade.bot/airdrop", "developer": "http://developer.dextrade.bot/", "userGuide": "https://docs.raidenx.io/docs/dca-order", "apiDocs": "https://public-api-docs.raidenx.io/docs/getting-started", "termsOfUse": "https://raidenx.io/docs/terms-of-use", "policy": "https://raidenx.io/docs/privacy-policy", "roadMap": "https://raidenx.canny.io/", "customerSupport": "https://t.me/raidenx_cs", "brandKit": "https://raidenx.io/media-kit"}}, "sponsorAddress": "0x34b2c6c1ef038334f2bc5142d0613c7b5f6ec5a67ae4a7cf37340adefdf2cfb6", "verifyToken": {"limited": {"price": 0.002}, "standard": {"price": 0.004}}, "minGasFee": 0.15, "raidenxFeeRate": 0.01, "raidenxFeeReceiverAddress": "0x0d66d7ac45c1d011fdd21c5d4d193913c8c40d5f1e1e62e33f72ffa0f2b86e2a", "networks": {"sui": {"network": "mainnet", "rpcUrl": "https://sui-mainnet.blockvision.org/v1/2kHLFHq7BILCeAxuyUck07bKMRT", "explorerUrl": "https://suivision.xyz", "treasuryAddress": "0x0db7989b98d681455f424035e3f01c02e27f738fdd6634ef34dedf576a9d8cea", "endpoints": {"domainNameApi": "https://api-suins.raidenx.io/api/v1", "walletApi": "https://uat-api-user-wallets-raidenx.sotalabs.io/api/v1", "orderApi": "https://uat-api-orders-raidenx.sotalabs.io/api/v1", "marketApi": "https://uat-api-raidenx.sotalabs.io/api/v1"}, "quoteTokens": [{"address": "******************************************000000000000000000000002::sui::SUI", "symbol": "SUI", "decimals": 9}, {"address": "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC", "symbol": "USDC", "decimals": 6}, {"address": "0xbc732bc5f1e9a9f4bdf4c0672ee538dbf56c161afe04ff1de2176efabdf41f92::suai::SUAI", "symbol": "SUAI", "decimals": 6}]}, "hyperevm": {"network": "mainnet", "rpcUrl": "", "explorerUrl": "", "treasuryAddress": "", "endpoints": {"walletApi": "https://uat-hyperevm-wallet-api-raidenx.sotalabs.io/api/v1", "orderApi": "https://uat-hyperevm-orders-api-raidenx.sotalabs.io/api/v1", "marketApi": "https://uat-hyperevm-market-api-raidenx.sotalabs.io/api/v1"}, "quoteTokens": [{"address": "******************************************", "symbol": "HYPE", "decimals": 18}]}, "somnia": {"network": "mainnet", "rpcUrl": "", "explorerUrl": "", "treasuryAddress": "", "endpoints": {"walletApi": "", "orderApi": "", "marketApi": ""}, "quoteTokens": [{"address": "******************************************", "symbol": "SOMNIA", "decimals": 18}]}, "plasma": {"network": "mainnet", "rpcUrl": "https://rpc.plasma.to", "explorerUrl": "https://plasmascan.to", "treasuryAddress": "", "endpoints": {"walletApi": "https://uat-hyperevm-wallet-api-raidenx.sotalabs.io/api/v1", "orderApi": "https://uat-hyperevm-orders-api-raidenx.sotalabs.io/api/v1", "marketApi": "https://uat-hyperevm-market-api-raidenx.sotalabs.io/api/v1"}, "quoteTokens": [{"address": "******************************************", "symbol": "XPL", "decimals": 18}]}}}