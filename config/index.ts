import dev from "./dev.json";
import prod from "./prod.json";
import staging from "./staging.json";

// Base network interface
export interface BaseNetwork {
  network: string;
  rpcUrl: string;
  explorerUrl: string;
  treasuryAddress: string;
}

export interface QuoteToken {
  address: string;
  symbol: string;
  decimals?: number;
  icon?: string;
}

// Specific network interfaces
export interface SuiNetwork extends BaseNetwork {
  network: "testnet" | "mainnet";
  endpoints: {
    domainNameApi: string;
    walletApi: string;
    orderApi: string;
    marketApi: string;
  };
  quoteTokens: QuoteToken[];
}

export interface HyperevmNetwork extends BaseNetwork {
  network: "testnet" | "mainnet";
  endpoints: {
    walletApi: string;
    orderApi: string;
    marketApi: string;
  };
  quoteTokens: QuoteToken[];
}

export interface SomniaNetwork extends BaseNetwork {
  network: "testnet" | "mainnet";
  endpoints: {
    walletApi: string;
    orderApi: string;
    marketApi: string;
  };
  quoteTokens: QuoteToken[];
}

export interface PlasmaNetwork extends BaseNetwork {
  network: "testnet" | "mainnet";
  endpoints: {
    walletApi: string;
    orderApi: string;
    marketApi: string;
  };
  quoteTokens: QuoteToken[];
}

// Union type for all supported networks
export type SupportedNetwork =
  | SuiNetwork
  | HyperevmNetwork
  | SomniaNetwork
  | PlasmaNetwork;

export interface Config {
  title: string;
  appUrl: string;
  newOrderUrl: string;
  endpoints: {
    ws: string;
    commonApi: string;
    webhookApi: string;
    userApi: string;
    alertApi: string;
    referralApi: string;
    insightApi: string;
    externalApi: string;
    authorizeApi?: string;
  };
  privyConfig: {
    appId: string;
    clientId: string;
    signerId: string;
  };
  network: string;
  link_telegram: string;
  linkSocial: {
    twitter: string;
    telegram: string;
    discord: string;
  };
  customerSupportUrl: string;
  developerUrl: string;
  treasuryAddress: string;
  homePage: {
    link: {
      telegramPremiumSignal: string;
      buyBot: string;
      airdrop: string;
      developer: string;
      userGuide: string;
      apiDocs: string;
      termsOfUse: string;
      policy: string;
      roadMap: string;
      brandKit: string;
      customerSupport: string;
    };
  };
  verifyToken: {
    limited: {
      price: number;
    };
    standard: {
      price: number;
    };
  };
  sponsorAddress: string;
  minGasFee: number;
  raidenxFeeRate: number;
  raidenxFeeReceiverAddress: string;
  networks?: {
    sui?: SuiNetwork;
    hyperevm?: HyperevmNetwork;
    somnia?: SomniaNetwork;
    plasma?: PlasmaNetwork;
  };
}

export const envConfig = process.env.NEXT_PUBLIC_ENV || "prod";

interface EnvConfig {
  prod: Config;
  dev: Config;
  staging: Config;
}

const configs: EnvConfig = { dev, prod, staging } as EnvConfig;
const config: Config = configs[envConfig as keyof typeof configs];

export default config;
